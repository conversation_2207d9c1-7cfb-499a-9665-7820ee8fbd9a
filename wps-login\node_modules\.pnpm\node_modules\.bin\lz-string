#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/wpscode/zhouxinyi21/wps-login/node_modules/.pnpm/lz-string@1.5.0/node_modules/lz-string/bin/node_modules:/mnt/d/wpscode/zhouxinyi21/wps-login/node_modules/.pnpm/lz-string@1.5.0/node_modules/lz-string/node_modules:/mnt/d/wpscode/zhouxinyi21/wps-login/node_modules/.pnpm/lz-string@1.5.0/node_modules:/mnt/d/wpscode/zhouxinyi21/wps-login/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/wpscode/zhouxinyi21/wps-login/node_modules/.pnpm/lz-string@1.5.0/node_modules/lz-string/bin/node_modules:/mnt/d/wpscode/zhouxinyi21/wps-login/node_modules/.pnpm/lz-string@1.5.0/node_modules/lz-string/node_modules:/mnt/d/wpscode/zhouxinyi21/wps-login/node_modules/.pnpm/lz-string@1.5.0/node_modules:/mnt/d/wpscode/zhouxinyi21/wps-login/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../lz-string/bin/bin.js" "$@"
else
  exec node  "$basedir/../lz-string/bin/bin.js" "$@"
fi
