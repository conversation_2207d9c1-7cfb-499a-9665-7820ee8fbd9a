{"name": "wps-login", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "lint:fix": "eslint . --fix", "proxy": "w2 add --force", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "coverage": "vitest run --coverage"}, "dependencies": {"axios": "^1.10.0", "framer-motion": "^12.23.6", "qs": "^6.14.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/qs": "^6.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vitest": "^3.2.4"}}