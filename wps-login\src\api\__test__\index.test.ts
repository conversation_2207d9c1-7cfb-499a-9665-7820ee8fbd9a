import axios from "axios";
import qs from "qs";
import { beforeEach, describe, expect, it, vi } from "vitest";
import type { VerifyType } from "../../type";
import type { LoginParams } from "../index";
import { fetchLogin, fetchStartVerify } from "../index";

vi.mock("axios");
const mockedAxios = axios as unknown as {
  post: ReturnType<typeof vi.fn>;
  get: ReturnType<typeof vi.fn>;
};

beforeEach(() => {
  vi.clearAllMocks();
});

describe("fetchStartVerify", () => {
  it("成功时返回 VerifyType 数据", async () => {
    const mockData: VerifyType = {
      result: "ok",
      ssid: "mock-ssid"
    };
    mockedAxios.post = vi.fn().mockResolvedValueOnce({ data: mockData });

    const result = await fetchStartVerify("13800138000", "885688");

    expect(result).toEqual(mockData);
    expect(mockedAxios.post).toHaveBeenCalledWith(
      "/api/v3/sms/verify",
      expect.stringContaining(
        qs.stringify({ phone: "13800138000", smscode: "885688", keeponline: 1 })
      ),
      expect.objectContaining({
        headers: { "Content-Type": "application/x-www-form-urlencoded" }
      })
    );
  });

  it("请求失败时抛出错误", async () => {
    mockedAxios.post = vi
      .fn()
      .mockRejectedValueOnce(new Error("Network Error"));
    await expect(fetchStartVerify("13800138000", "123456")).rejects.toThrow(
      "Network Error"
    );
  });
});

describe("fetchLogin", () => {
  it("成功时返回用户数据", async () => {
    const mockData = { result: "ok", user: { id: "123", name: "Alice" } };
    mockedAxios.get = vi.fn().mockResolvedValueOnce({ data: mockData });
    const params: LoginParams = {
      ssid: "mock-ssid",
      filter_rule: "rule1",
      check_rule: "rule2",
      _: "timestamp"
    };

    const result = await fetchLogin(params);
    expect(result).toEqual(mockData);
    expect(mockedAxios.get).toHaveBeenCalledWith("/api/v3/login/users", {
      params
    });
  });

  it("请求失败时抛出错误", async () => {
    mockedAxios.get = vi
      .fn()
      .mockRejectedValueOnce(new Error("Request failed"));
    const params: LoginParams = {
      ssid: "mock-ssid",
      filter_rule: "rule1",
      check_rule: "rule2",
      _: "timestamp"
    };
    await expect(fetchLogin(params)).rejects.toThrow("Request failed");
  });
});
