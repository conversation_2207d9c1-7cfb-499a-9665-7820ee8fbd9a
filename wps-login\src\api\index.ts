import axios from "axios";
import qs from "qs";
import type { VerifyType } from "../type";
export interface LoginParams {
  ssid: string;
  filter_rule: string;
  check_rule: string;
  _: string;
}

axios.defaults.withCredentials = true;

//点击登录先获取ssidapi
export const fetchStartVerify = async (
  phoneNumber: string,
  verificationCode: string
): Promise<VerifyType> => {
  const data = qs.stringify({
    phone: phoneNumber,
    smscode: verificationCode,
    keeponline: 1
  });

  const response = await axios.post<VerifyType>("/api/v3/sms/verify", data, {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  });

  // 如果验证码不等于 885688，修改返回的数据
  // if (verificationCode !== "885688") {
  //   return {
  //     result: "InvalidSMSCode",
  //     msg: "验证码不正确，请重新输入"
  //   };
  // }
  console.log("🚀 ~~ 获取ssid ~~ response", response);
  return response.data;
};

//点击立即登录/注册
export const fetchLogin = async (params: LoginParams) => {
  const { ssid, filter_rule, check_rule, _ } = params;
  console.log("登录参数：", ssid, filter_rule, check_rule, _);
  try {
    const response = await axios.get("/api/v3/login/users", {
      params: { ssid, filter_rule, check_rule, _ }
    });
    console.log("🚀 ~~立即登录/注册 ~~ response 🤖--EndLog--🤖", response);
    return response.data;
  } catch (error) {
    console.error("Error during login:", error);
    throw error;
  }
};
