import { act, renderHook } from "@testing-library/react";
import { beforeEach, describe, expect, it } from "vitest";
import { MOBILE_BREAKPOINT } from "../../constants";
import { useMobile } from "../useMobile";

describe("useMobile Hook", () => {
  beforeEach(() => {
    // 重置 window.innerWidth
    (window.innerWidth as number) = 1024;
  });

  it("初始化时根据窗口宽度返回正确值", () => {
    window.innerWidth = 500; // 模拟移动端
    const { result } = renderHook(() => useMobile());
    expect(result.current).toBe(true);
  });

  it("初始化时根据大屏幕返回 false", () => {
    window.innerWidth = 1200;
    const { result } = renderHook(() => useMobile());
    expect(result.current).toBe(false);
  });

  it("窗口缩放后更新状态", () => {
    const { result } = renderHook(() => useMobile());

    // 模拟 resize
    act(() => {
      window.innerWidth = MOBILE_BREAKPOINT - 10;
      window.dispatchEvent(new Event("resize"));
    });

    expect(result.current).toBe(true);

    act(() => {
      window.innerWidth = MOBILE_BREAKPOINT + 100;
      window.dispatchEvent(new Event("resize"));
    });

    expect(result.current).toBe(false);
  });
});
