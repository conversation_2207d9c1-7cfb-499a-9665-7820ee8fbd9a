import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { beforeEach, describe, expect, it, vi } from "vitest";
import * as useMobileHook from "../../../hooks/useMobile";
import type { User } from "../../../type";
import Home from "../index";

// Mock所有子组件
vi.mock("../../QRCodeLogin", () => ({
  default: ({ onCurrentBtn }: { onCurrentBtn: (value: string) => void }) => (
    <div data-testid="qrcode-login">
      <button onClick={() => onCurrentBtn("phone")}>切换到手机登录</button>
      <button onClick={() => onCurrentBtn("sso")}>SSO登录</button>
      <button onClick={() => onCurrentBtn("more")}>更多登录方式</button>
      <button onClick={() => onCurrentBtn("qq")}>QQ登录</button>
    </div>
  )
}));

vi.mock("../../PhoneLogin", () => ({
  default: ({
    onBack,
    onLoginSuccess,
    onCurrentBtn
  }: {
    onBack: () => void;
    onLoginSuccess: (users: User[]) => void;
    onCurrentBtn: (value: string) => void;
  }) => (
    <div data-testid="phone-login">
      <button onClick={onBack}>返回</button>
      <button
        onClick={() =>
          onLoginSuccess([
            {
              userid: 1,
              nickname: "测试用户",
              company_id: 1,
              company_name: "测试公司",
              avatar_url: "",
              is_current: true,
              loginmode: "phone"
            }
          ])
        }
      >
        登录成功
      </button>
      <button onClick={() => onCurrentBtn("sso")}>SSO登录</button>
    </div>
  )
}));

vi.mock("../../SSOLogin", () => ({
  default: ({ onBack }: { onBack: () => void }) => (
    <div data-testid="sso-login">
      <button onClick={onBack}>返回</button>
    </div>
  )
}));

vi.mock("../../ExCompany", () => ({
  default: ({ onBack }: { onBack: () => void }) => (
    <div data-testid="ex-company">
      <button onClick={onBack}>返回</button>
    </div>
  )
}));

vi.mock("../../UsersList", () => ({
  default: ({ users, onBack }: { users: User[]; onBack: () => void }) => (
    <div data-testid="users-list">
      <div>用户数量: {users.length}</div>
      <button onClick={onBack}>返回</button>
    </div>
  )
}));

vi.mock("../../../components/Modal", () => ({
  default: ({
    isOpen,
    onClose,
    onConfirm
  }: {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
  }) =>
    isOpen ? (
      <div data-testid="modal">
        <button onClick={onClose}>关闭</button>
        <button onClick={onConfirm}>确认</button>
      </div>
    ) : null
}));

vi.mock("../../../hooks/useMobile", () => ({
  useMobile: vi.fn()
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};
Object.defineProperty(window, "localStorage", {
  value: localStorageMock
});

// Mock alert
Object.defineProperty(window, "alert", {
  value: vi.fn(),
  writable: true
});

describe("Home组件", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    vi.mocked(useMobileHook.useMobile).mockReturnValue(false);
  });

  it("桌面端默认显示二维码登录", () => {
    vi.mocked(useMobileHook.useMobile).mockReturnValue(false);
    render(<Home />);

    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
    expect(screen.queryByTestId("phone-login")).not.toBeInTheDocument();
  });

  it("移动端默认显示手机登录", () => {
    vi.mocked(useMobileHook.useMobile).mockReturnValue(true);
    render(<Home />);

    expect(screen.getByTestId("phone-login")).toBeInTheDocument();
    expect(screen.queryByTestId("qrcode-login")).not.toBeInTheDocument();
  });

  it("从localStorage读取协议同意状态", () => {
    localStorageMock.getItem.mockReturnValue("true");
    render(<Home />);

    expect(localStorageMock.getItem).toHaveBeenCalledWith(
      "wps-protocol-agreed"
    );
  });

  it("已同意协议时直接跳转到对应登录方式", async () => {
    localStorageMock.getItem.mockReturnValue("true");
    const user = userEvent.setup();
    render(<Home />);

    const ssoButton = screen.getByText("SSO登录");
    await user.click(ssoButton);

    expect(screen.getByTestId("sso-login")).toBeInTheDocument();
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("未同意协议时显示Modal", async () => {
    localStorageMock.getItem.mockReturnValue(null);
    const user = userEvent.setup();
    render(<Home />);

    const ssoButton = screen.getByText("SSO登录");
    await user.click(ssoButton);

    expect(screen.getByTestId("modal")).toBeInTheDocument();
    expect(screen.queryByTestId("sso-login")).not.toBeInTheDocument();
  });

  it("确认协议后保存状态并跳转", async () => {
    localStorageMock.getItem.mockReturnValue(null);
    const user = userEvent.setup();
    render(<Home />);

    // 点击SSO登录触发Modal
    const ssoButton = screen.getByText("SSO登录");
    await user.click(ssoButton);

    // 确认协议
    const confirmButton = screen.getByText("确认");
    await user.click(confirmButton);

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      "wps-protocol-agreed",
      "true"
    );
    expect(screen.getByTestId("sso-login")).toBeInTheDocument();
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("关闭Modal后不跳转", async () => {
    localStorageMock.getItem.mockReturnValue(null);
    const user = userEvent.setup();
    render(<Home />);

    // 点击SSO登录触发Modal
    const ssoButton = screen.getByText("SSO登录");
    await user.click(ssoButton);

    // 关闭Modal
    const closeButton = screen.getByText("关闭");
    await user.click(closeButton);

    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
    expect(screen.queryByTestId("sso-login")).not.toBeInTheDocument();
    expect(screen.queryByTestId("modal")).not.toBeInTheDocument();
  });

  it("QQ登录显示开发中提示", async () => {
    localStorageMock.getItem.mockReturnValue("true");
    const user = userEvent.setup();
    render(<Home />);

    const qqButton = screen.getByText("QQ登录");
    await user.click(qqButton);

    expect(window.alert).toHaveBeenCalledWith("QQ登录功能开发中...");
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
  });

  it("从手机登录返回到二维码登录", async () => {
    vi.mocked(useMobileHook.useMobile).mockReturnValue(false);
    localStorageMock.getItem.mockReturnValue("true"); // 已同意协议
    const user = userEvent.setup();
    render(<Home />);

    // 切换到手机登录
    const phoneButton = screen.getByText("切换到手机登录");
    await user.click(phoneButton);
    expect(screen.getByTestId("phone-login")).toBeInTheDocument();

    // 返回
    const backButton = screen.getByText("返回");
    await user.click(backButton);
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
  });

  it("登录成功后显示用户列表", async () => {
    localStorageMock.getItem.mockReturnValue("true"); // 已同意协议
    const user = userEvent.setup();
    render(<Home />);

    // 切换到手机登录
    const phoneButton = screen.getByText("切换到手机登录");
    await user.click(phoneButton);
    expect(screen.getByTestId("phone-login")).toBeInTheDocument();

    // 模拟登录成功
    const loginButton = screen.getByText("登录成功");
    await user.click(loginButton);

    expect(screen.getByTestId("users-list")).toBeInTheDocument();
    expect(screen.getByText("用户数量: 1")).toBeInTheDocument();
  });

  it("从用户列表返回到二维码登录", async () => {
    localStorageMock.getItem.mockReturnValue("true"); // 已同意协议
    const user = userEvent.setup();
    render(<Home />);

    // 切换到手机登录并登录成功
    const phoneButton = screen.getByText("切换到手机登录");
    await user.click(phoneButton);
    expect(screen.getByTestId("phone-login")).toBeInTheDocument();

    const loginButton = screen.getByText("登录成功");
    await user.click(loginButton);
    expect(screen.getByTestId("users-list")).toBeInTheDocument();

    // 从用户列表返回
    const backButton = screen.getByText("返回");
    await user.click(backButton);

    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
    expect(screen.queryByTestId("users-list")).not.toBeInTheDocument();
  });

  it("more登录方式跳转到ExCompany", async () => {
    localStorageMock.getItem.mockReturnValue("true");
    const user = userEvent.setup();
    render(<Home />);

    const moreButton = screen.getByText("更多登录方式");
    await user.click(moreButton);

    expect(screen.getByTestId("ex-company")).toBeInTheDocument();
    expect(screen.queryByTestId("qrcode-login")).not.toBeInTheDocument();
  });

  it("从ExCompany返回到二维码登录", async () => {
    localStorageMock.getItem.mockReturnValue("true");
    const user = userEvent.setup();
    render(<Home />);

    // 跳转到ExCompany
    const moreButton = screen.getByText("更多登录方式");
    await user.click(moreButton);
    expect(screen.getByTestId("ex-company")).toBeInTheDocument();

    // 返回
    const backButton = screen.getByText("返回");
    await user.click(backButton);
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
  });

  it("从SSO登录返回到二维码登录", async () => {
    localStorageMock.getItem.mockReturnValue("true");
    const user = userEvent.setup();
    render(<Home />);

    // 跳转到SSO登录
    const ssoButton = screen.getByText("SSO登录");
    await user.click(ssoButton);
    expect(screen.getByTestId("sso-login")).toBeInTheDocument();

    // 返回
    const backButton = screen.getByText("返回");
    await user.click(backButton);
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
  });

  it("在手机登录页面切换到SSO登录", async () => {
    localStorageMock.getItem.mockReturnValue("true");
    const user = userEvent.setup();
    render(<Home />);

    // 切换到手机登录
    const phoneButton = screen.getByText("切换到手机登录");
    await user.click(phoneButton);
    expect(screen.getByTestId("phone-login")).toBeInTheDocument();

    // 在手机登录页面点击SSO登录
    const ssoButton = screen.getByText("SSO登录");
    await user.click(ssoButton);
    expect(screen.getByTestId("sso-login")).toBeInTheDocument();
  });

  it("测试所有登录方式的状态切换", async () => {
    localStorageMock.getItem.mockReturnValue("true");
    const user = userEvent.setup();
    render(<Home />);

    // 初始状态：二维码登录
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();

    // 切换到手机登录
    await user.click(screen.getByText("切换到手机登录"));
    expect(screen.getByTestId("phone-login")).toBeInTheDocument();
    expect(screen.queryByTestId("qrcode-login")).not.toBeInTheDocument();

    // 返回二维码登录
    await user.click(screen.getByText("返回"));
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();

    // 切换到SSO登录
    await user.click(screen.getByText("SSO登录"));
    expect(screen.getByTestId("sso-login")).toBeInTheDocument();

    // 返回二维码登录
    await user.click(screen.getByText("返回"));
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();

    // 切换到ExCompany
    await user.click(screen.getByText("更多登录方式"));
    expect(screen.getByTestId("ex-company")).toBeInTheDocument();

    // 返回二维码登录
    await user.click(screen.getByText("返回"));
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
  });

  it("移动端环境下的状态切换", async () => {
    vi.mocked(useMobileHook.useMobile).mockReturnValue(true);
    localStorageMock.getItem.mockReturnValue("true");
    const user = userEvent.setup();
    render(<Home />);

    // 移动端初始状态：手机登录
    expect(screen.getByTestId("phone-login")).toBeInTheDocument();

    // 切换到SSO登录
    await user.click(screen.getByText("SSO登录"));
    expect(screen.getByTestId("sso-login")).toBeInTheDocument();

    // 返回手机登录
    await user.click(screen.getByText("返回"));
    expect(screen.getByTestId("phone-login")).toBeInTheDocument();
  });

  it("协议状态为false字符串时的处理", () => {
    localStorageMock.getItem.mockReturnValue("false");
    render(<Home />);

    expect(localStorageMock.getItem).toHaveBeenCalledWith(
      "wps-protocol-agreed"
    );
    // 应该仍然显示默认登录方式
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
  });

  it("localStorage返回空字符串时的处理", () => {
    localStorageMock.getItem.mockReturnValue("");
    render(<Home />);

    // 空字符串应该被视为未同意协议
    expect(screen.getByTestId("qrcode-login")).toBeInTheDocument();
  });
});
