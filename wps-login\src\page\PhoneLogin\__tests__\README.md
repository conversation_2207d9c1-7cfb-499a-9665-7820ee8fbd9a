# PhoneLogin 组件测试

PhoneLogin 组件的测试，使用 Vitest 和 React Testing Library。

## 测试范围

### 1. 功能测试 (`PhoneLogin.test.tsx`)

- ✅ 基本渲染测试
- ✅ 桌面端/移动端显示差异测试
- ✅ 用户输入功能测试
- ✅ 智能验证流程测试
- ✅ 验证码输入和发送测试
- ✅ 完整登录流程测试
- ✅ 表单验证测试（区分手机号和验证码验证）
- ✅ 回调函数测试

### 运行特定测试文件

```bash
# 运行主要单元测试
pnpm run test PhoneLogin.test.tsx

# 运行 API 测试
pnpm run test PhoneLogin.api.test.tsx
```

### 运行测试并查看覆盖率

```bash
pnpm run test:coverage
```

### 运行测试 UI

```bash
pnpm run test:ui
```
