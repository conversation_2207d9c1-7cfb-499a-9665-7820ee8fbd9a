import { vi } from "vitest";
import type { User } from "../../../type";

/**
 * 创建模拟用户数据
 */
export const createMockUser = (overrides: Partial<User> = {}): User => ({
  userid: 1,
  nickname: "测试用户",
  company_id: 1,
  company_name: "测试公司",
  company_logo: "",
  company_custom_domain: "",
  is_company_account: false,
  avatar_url: "",
  status: 1,
  reason: "",
  reason_v2: { key: "", translate: false },
  link_text: "",
  link_text_v2: { key: "", translate: false },
  link_url: "",
  is_current: true,
  is_login: false,
  loginmode: "phone",
  session_status: 1,
  logout_reason: "",
  need_tfa: false,
  default_select: true,
  last_active_time: Date.now(),
  ...overrides
});

/**
 * 创建模拟 props
 */
export const createMockProps = (overrides = {}) => ({
  onBack: vi.fn(),
  onLoginSuccess: vi.fn(),
  onCurrentBtn: vi.fn(),
  ...overrides
});
