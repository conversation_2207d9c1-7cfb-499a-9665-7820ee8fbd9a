import { describe, expect, it } from "vitest";
import { isValidInternationalPhone } from "../phone";

describe("isValidInternationalPhone - Parameterized Tests", () => {
  it.each([
    //合法
    ["+8613812345678", true],
    ["*************", true],
    ["+44 7911123456", true],
    ["918888888888", true],
    ["+33123456789", true],
    ["819012345678", true],

    // 非法
    ["+86 12345", false],
    ["+86123456789012345", false],
    ["+86abc12345", false],
    ["++8613812345678", false],
    ["86_13812345678", false],
    ["   ", false],
    ["", false]
  ])("should return %s for input '%s'", (input, expected) => {
    expect(isValidInternationalPhone(input)).toBe(expected);
  });
});
